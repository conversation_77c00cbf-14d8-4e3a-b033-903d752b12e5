using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Interop;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using SharpDX;
using SharpDX.Direct3D11;
using SharpDX.DXGI;
using Device = SharpDX.Direct3D11.Device;
using MapFlags = SharpDX.Direct3D11.MapFlags;

namespace combatLoa
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll")]
        private static extern int GetWindowTextLength(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hwnd, ref RECT rectangle);

        [DllImport("user32.dll")]
        private static extern bool GetClientRect(IntPtr hWnd, ref RECT lpRect);

        [DllImport("user32.dll")]
        private static extern bool ClientToScreen(IntPtr hWnd, ref POINT lpPoint);

        [DllImport("user32.dll")]
        private static extern bool PrintWindow(IntPtr hwnd, IntPtr hdcBlt, uint nFlags);

        [DllImport("user32.dll")]
        private static extern IntPtr GetWindowDC(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern IntPtr ReleaseDC(IntPtr hWnd, IntPtr hDC);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleDC(IntPtr hdc);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleBitmap(IntPtr hdc, int nWidth, int nHeight);

        [DllImport("gdi32.dll")]
        private static extern IntPtr SelectObject(IntPtr hdc, IntPtr hgdiobj);

        [DllImport("gdi32.dll")]
        private static extern bool BitBlt(IntPtr hdc, int nXDest, int nYDest, int nWidth, int nHeight, IntPtr hdcSrc, int nXSrc, int nYSrc, uint dwRop);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteDC(IntPtr hdc);

        [DllImport("dwmapi.dll")]
        private static extern int DwmGetWindowAttribute(IntPtr hwnd, int dwAttribute, out RECT pvAttribute, int cbAttribute);

        private const int DWMWA_EXTENDED_FRAME_BOUNDS = 9;

        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct POINT
        {
            public int X;
            public int Y;
        }

        private IntPtr lostArkWindow = IntPtr.Zero;
        private System.Windows.Threading.DispatcherTimer captureTimer;

        // DirectX 관련 변수들
        private Device device;
        private OutputDuplication outputDuplication;
        private bool directXInitialized = false;

        // OCR 영역 선택 관련 변수들
        private bool isOcrModeEnabled = false;
        private bool isSelecting = false;
        private System.Windows.Point startPoint;
        private System.Windows.Point endPoint;
        private Rectangle selectedOcrArea = Rectangle.Empty;
        private Canvas selectionCanvas = null;

        public MainWindow()
        {
            InitializeComponent();
            InitializeDirectX();
        }

        private void InitializeDirectX()
        {
            try
            {
                // 여러 어댑터를 시도해보기
                using (var factory = new Factory1())
                {
                    for (int i = 0; i < factory.GetAdapterCount1(); i++)
                    {
                        try
                        {
                            using (var adapter = factory.GetAdapter1(i))
                            {
                                // DirectX 디바이스 생성 (어댑터별로 시도)
                                device = new Device(adapter, DeviceCreationFlags.None);

                                // 출력 장치 찾기
                                for (int j = 0; j < adapter.GetOutputCount(); j++)
                                {
                                    try
                                    {
                                        using (var output = adapter.GetOutput(j))
                                        using (var output1 = output.QueryInterface<Output1>())
                                        {
                                            // Desktop Duplication API 초기화
                                            outputDuplication = output1.DuplicateOutput(device);
                                            directXInitialized = true;
                                            StatusText.Text = $"DirectX 초기화 완료 (어댑터 {i}, 출력 {j})";
                                            return;
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        StatusText.Text = $"출력 {j} 초기화 실패: {ex.Message}";
                                        continue;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            StatusText.Text = $"어댑터 {i} 초기화 실패: {ex.Message}";
                            continue;
                        }
                    }
                }

                // 모든 어댑터가 실패한 경우 기본 방법 시도
                if (!directXInitialized)
                {
                    StatusText.Text = "기본 DirectX 초기화 시도 중...";
                    device = new Device(SharpDX.Direct3D.DriverType.Hardware, DeviceCreationFlags.None);

                    using (var factory = new Factory1())
                    using (var adapter = factory.GetAdapter1(0))
                    using (var output = adapter.GetOutput(0))
                    using (var output1 = output.QueryInterface<Output1>())
                    {
                        outputDuplication = output1.DuplicateOutput(device);
                        directXInitialized = true;
                        StatusText.Text = "DirectX 기본 초기화 완료";
                    }
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = $"DirectX 초기화 완전 실패: {ex.Message}";
                directXInitialized = false;

                // DirectX 실패 시 GDI+ 방법으로 대체
                StatusText.Text += " - GDI+ 방법으로 대체됩니다.";
            }
        }

        private void StartButton_Click(object sender, RoutedEventArgs e)
        {
            DetectLostArkWindow();
        }

        private void DetectButton_Click(object sender, RoutedEventArgs e)
        {
            DetectLostArkWindow();
        }

        private void DetectLostArkWindow()
        {
            lostArkWindow = IntPtr.Zero;
            EnumWindows(EnumWindowCallback, IntPtr.Zero);

            if (lostArkWindow != IntPtr.Zero)
            {
                StatusText.Text = "LOST ARK 창이 감지되었습니다!";
                StartButton.Visibility = Visibility.Collapsed;
                DetectButton.Visibility = Visibility.Collapsed;
                OcrModeButton.Visibility = Visibility.Visible;

                StartContinuousCapture(); // DirectX 실패해도 GDI+로 캡처
            }
            else
            {
                StatusText.Text = "게임을 실행해주세요";
                StartButton.Visibility = Visibility.Collapsed;
                DetectButton.Visibility = Visibility.Visible;
                OcrModeButton.Visibility = Visibility.Collapsed;
            }
        }

        private void OcrModeButton_Click(object sender, RoutedEventArgs e)
        {
            isOcrModeEnabled = !isOcrModeEnabled;

            if (isOcrModeEnabled)
            {
                OcrModeButton.Content = "OCR 모드 해제";
                StatusText.Text = "OCR 영역을 드래그로 선택하세요";
            }
            else
            {
                OcrModeButton.Content = "OCR 영역 선택";
                StatusText.Text = "캡처 중...";
                selectedOcrArea = Rectangle.Empty;

                // 선택 영역 표시 제거
                if (selectionCanvas != null && imageWindow != null)
                {
                    var scrollViewer = imageWindow.Content as ScrollViewer;
                    if (scrollViewer?.Content is Grid grid)
                    {
                        grid.Children.Remove(selectionCanvas);
                    }
                    selectionCanvas = null;
                }
            }
        }

        private bool EnumWindowCallback(IntPtr hWnd, IntPtr lParam)
        {
            if (IsWindowVisible(hWnd))
            {
                int length = GetWindowTextLength(hWnd);
                if (length > 0)
                {
                    StringBuilder sb = new StringBuilder(length + 1);
                    GetWindowText(hWnd, sb, sb.Capacity);
                    string windowTitle = sb.ToString();

                    if (windowTitle.Contains("LOST ARK"))
                    {
                        lostArkWindow = hWnd;
                        return false;
                    }
                }
            }
            return true;
        }

        private void StartContinuousCapture()
        {
            captureTimer = new System.Windows.Threading.DispatcherTimer();
            captureTimer.Interval = TimeSpan.FromMilliseconds(33); // 약 30 FPS
            captureTimer.Tick += (s, e) => CaptureWithDirectX();

            CaptureWithDirectX();
            captureTimer.Start();
        }

        private void CaptureWithDirectX()
        {
            try
            {
                // Lost Ark 창의 클라이언트 영역 크기 가져오기
                RECT clientRect = new RECT();
                GetClientRect(lostArkWindow, ref clientRect);

                int width = clientRect.Right - clientRect.Left;
                int height = clientRect.Bottom - clientRect.Top;

                if (width <= 0 || height <= 0)
                {
                    StatusText.Text = "Lost Ark 창 크기를 가져올 수 없습니다.";
                    return;
                }

                // 상태 텍스트에 캡처 중인 해상도 표시
                StatusText.Text = $"캡처 중... ({width}x{height})";

                // Lost Ark 창 직접 캡처 (다른 창에 가려져도 캡처됨)
                Bitmap bitmap = CaptureWindowDirectly(lostArkWindow, width, height);

                if (bitmap != null)
                {
                    ShowOrUpdateCapturedImage(bitmap);
                }
                else
                {
                    StatusText.Text = "캡처 실패 - 다시 시도 중...";
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = $"캡처 오류: {ex.Message}";
            }
        }

        private Bitmap CaptureWindowDirectly(IntPtr windowHandle, int width, int height)
        {
            try
            {
                // 창의 실제 클라이언트 영역 계산
                RECT windowRect = new RECT();
                RECT clientRect = new RECT();

                GetWindowRect(windowHandle, ref windowRect);
                GetClientRect(windowHandle, ref clientRect);

                // 클라이언트 영역의 화면 좌표 계산
                POINT clientTopLeft = new POINT { X = 0, Y = 0 };
                ClientToScreen(windowHandle, ref clientTopLeft);

                // 실제 클라이언트 크기
                int clientWidth = clientRect.Right - clientRect.Left;
                int clientHeight = clientRect.Bottom - clientRect.Top;

                // 방법 1: PrintWindow API 사용 (클라이언트 영역만)
                Bitmap bitmap = new Bitmap(clientWidth, clientHeight, PixelFormat.Format32bppArgb);
                using (Graphics graphics = Graphics.FromImage(bitmap))
                {
                    IntPtr hdc = graphics.GetHdc();

                    // 클라이언트 영역만 캡처하기 위해 오프셋 계산
                    int titleBarHeight = clientTopLeft.Y - windowRect.Top;
                    int leftBorderWidth = clientTopLeft.X - windowRect.Left;

                    // PrintWindow로 전체 창 캡처 후 클라이언트 영역만 추출
                    using (var fullBitmap = new Bitmap(windowRect.Right - windowRect.Left, windowRect.Bottom - windowRect.Top))
                    {
                        using (var fullGraphics = Graphics.FromImage(fullBitmap))
                        {
                            IntPtr fullHdc = fullGraphics.GetHdc();
                            bool result = PrintWindow(windowHandle, fullHdc, 0x00000002);
                            fullGraphics.ReleaseHdc(fullHdc);

                            if (result)
                            {
                                // 클라이언트 영역만 복사
                                graphics.DrawImage(fullBitmap,
                                    new Rectangle(0, 0, clientWidth, clientHeight),
                                    new Rectangle(leftBorderWidth, titleBarHeight, clientWidth, clientHeight),
                                    GraphicsUnit.Pixel);

                                graphics.ReleaseHdc(hdc);
                                return bitmap;
                            }
                        }
                    }

                    graphics.ReleaseHdc(hdc);
                }
                bitmap.Dispose();

                // 방법 2: 직접 클라이언트 DC 사용
                return CaptureClientArea(windowHandle, clientWidth, clientHeight);
            }
            catch (Exception ex)
            {
                StatusText.Text = $"창 캡처 실패: {ex.Message}";
                return null;
            }
        }

        private Bitmap CaptureClientArea(IntPtr windowHandle, int width, int height)
        {
            IntPtr clientDC = IntPtr.Zero;
            IntPtr memoryDC = IntPtr.Zero;
            IntPtr bitmap = IntPtr.Zero;
            IntPtr oldBitmap = IntPtr.Zero;

            try
            {
                // 클라이언트 영역의 DC 가져오기
                clientDC = GetDC(windowHandle);
                if (clientDC == IntPtr.Zero) return null;

                // 메모리 DC 생성
                memoryDC = CreateCompatibleDC(clientDC);
                if (memoryDC == IntPtr.Zero) return null;

                // 호환 비트맵 생성
                bitmap = CreateCompatibleBitmap(clientDC, width, height);
                if (bitmap == IntPtr.Zero) return null;

                // 비트맵을 메모리 DC에 선택
                oldBitmap = SelectObject(memoryDC, bitmap);

                // 클라이언트 영역 내용을 메모리 DC로 복사
                bool success = BitBlt(memoryDC, 0, 0, width, height, clientDC, 0, 0, 0x00CC0020);

                if (success)
                {
                    return System.Drawing.Image.FromHbitmap(bitmap);
                }

                return null;
            }
            catch
            {
                return null;
            }
            finally
            {
                if (oldBitmap != IntPtr.Zero) SelectObject(memoryDC, oldBitmap);
                if (bitmap != IntPtr.Zero) DeleteObject(bitmap);
                if (memoryDC != IntPtr.Zero) DeleteDC(memoryDC);
                if (clientDC != IntPtr.Zero) ReleaseDC(windowHandle, clientDC);
            }
        }

        [DllImport("user32.dll")]
        private static extern IntPtr GetDC(IntPtr hWnd);

        private Bitmap CaptureScreenWithDirectX(int x, int y, int width, int height)
        {
            try
            {
                SharpDX.DXGI.Resource screenResource;
                OutputDuplicateFrameInformation duplicateFrameInformation;

                // 화면 프레임 획득
                var result = outputDuplication.TryAcquireNextFrame(1000, out duplicateFrameInformation, out screenResource);

                if (!result.Success)
                {
                    return null;
                }

                using (screenResource)
                using (var screenTexture2D = screenResource.QueryInterface<Texture2D>())
                {
                    var textureDesc = screenTexture2D.Description;
                    textureDesc.CpuAccessFlags = CpuAccessFlags.Read;
                    textureDesc.Usage = ResourceUsage.Staging;
                    textureDesc.BindFlags = 0;
                    // MiscFlags 제거 (SharpDX 4.2.0에서는 지원하지 않음)

                    using (var stagingTexture = new Texture2D(device, textureDesc))
                    {
                        device.ImmediateContext.CopyResource(screenTexture2D, stagingTexture);

                        var dataBox = device.ImmediateContext.MapSubresource(stagingTexture, 0, MapMode.Read, MapFlags.None);

                        try
                        {
                            var bitmap = new Bitmap(width, height, PixelFormat.Format32bppArgb);
                            var bitmapData = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.WriteOnly, PixelFormat.Format32bppArgb);

                            try
                            {
                                unsafe
                                {
                                    byte* sourcePtr = (byte*)dataBox.DataPointer.ToPointer();
                                    byte* destPtr = (byte*)bitmapData.Scan0.ToPointer();

                                    for (int row = 0; row < height; row++)
                                    {
                                        // 원하는 영역만 복사
                                        byte* sourceRowPtr = sourcePtr + ((y + row) * dataBox.RowPitch) + (x * 4);
                                        byte* destRowPtr = destPtr + (row * bitmapData.Stride);

                                        for (int col = 0; col < width; col++)
                                        {
                                            destRowPtr[col * 4] = sourceRowPtr[col * 4];     // B
                                            destRowPtr[col * 4 + 1] = sourceRowPtr[col * 4 + 1]; // G
                                            destRowPtr[col * 4 + 2] = sourceRowPtr[col * 4 + 2]; // R
                                            destRowPtr[col * 4 + 3] = 255; // A
                                        }
                                    }
                                }
                            }
                            finally
                            {
                                bitmap.UnlockBits(bitmapData);
                            }

                            return bitmap;
                        }
                        finally
                        {
                            device.ImmediateContext.UnmapSubresource(stagingTexture, 0);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = $"DirectX 캡처 실패: {ex.Message}";
                return null;
            }
            finally
            {
                try
                {
                    outputDuplication.ReleaseFrame();
                }
                catch { }
            }
        }

        private Bitmap CaptureWithGDI(int x, int y, int width, int height)
        {
            try
            {
                Bitmap bitmap = new Bitmap(width, height, PixelFormat.Format32bppArgb);
                using (Graphics graphics = Graphics.FromImage(bitmap))
                {
                    graphics.CopyFromScreen(x, y, 0, 0, new System.Drawing.Size(width, height), CopyPixelOperation.SourceCopy);
                }
                return bitmap;
            }
            catch
            {
                return null;
            }
        }

        private Window imageWindow = null;
        private System.Windows.Controls.Image displayImage = null;

        private void ShowOrUpdateCapturedImage(Bitmap bitmap)
        {
            if (imageWindow == null || !imageWindow.IsVisible)
            {
                // Lost Ark 창 크기에 맞게 캡처 창 크기 설정
                int windowWidth = Math.Min(bitmap.Width + 40, (int)(SystemParameters.PrimaryScreenWidth * 0.9));
                int windowHeight = Math.Min(bitmap.Height + 80, (int)(SystemParameters.PrimaryScreenHeight * 0.9));

                imageWindow = new Window
                {
                    Title = "Lost Ark 캡처",
                    Width = windowWidth,
                    Height = windowHeight,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    WindowState = WindowState.Normal
                };

                var scrollViewer = new ScrollViewer
                {
                    HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                    VerticalScrollBarVisibility = ScrollBarVisibility.Auto
                };

                // Grid를 사용하여 이미지와 선택 영역을 겹쳐서 표시
                var grid = new Grid();

                displayImage = new System.Windows.Controls.Image
                {
                    Stretch = System.Windows.Media.Stretch.None,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };

                grid.Children.Add(displayImage);
                scrollViewer.Content = grid;
                imageWindow.Content = scrollViewer;

                imageWindow.Closed += (s, e) =>
                {
                    captureTimer?.Stop();
                    imageWindow = null;
                    displayImage = null;
                    selectionCanvas = null;
                };

                // Lost Ark 창이 너무 크면 최대화
                if (bitmap.Width > SystemParameters.PrimaryScreenWidth * 0.8 ||
                    bitmap.Height > SystemParameters.PrimaryScreenHeight * 0.8)
                {
                    imageWindow.WindowState = WindowState.Maximized;
                }

                // 마우스 이벤트 추가
                grid.MouseLeftButtonDown += Grid_MouseLeftButtonDown;
                grid.MouseMove += Grid_MouseMove;
                grid.MouseLeftButtonUp += Grid_MouseLeftButtonUp;
                grid.Background = System.Windows.Media.Brushes.Transparent; // 마우스 이벤트를 받기 위해 필요

                imageWindow.Show();
            }
            else
            {
                // 창이 이미 열려있으면 크기 업데이트
                if (Math.Abs(imageWindow.Width - (bitmap.Width + 40)) > 50 ||
                    Math.Abs(imageWindow.Height - (bitmap.Height + 80)) > 50)
                {
                    int newWidth = Math.Min(bitmap.Width + 40, (int)(SystemParameters.PrimaryScreenWidth * 0.9));
                    int newHeight = Math.Min(bitmap.Height + 80, (int)(SystemParameters.PrimaryScreenHeight * 0.9));

                    imageWindow.Width = newWidth;
                    imageWindow.Height = newHeight;
                }
            }

            IntPtr hBitmap = bitmap.GetHbitmap();
            try
            {
                BitmapSource bitmapSource = Imaging.CreateBitmapSourceFromHBitmap(
                    hBitmap, IntPtr.Zero, Int32Rect.Empty, BitmapSizeOptions.FromEmptyOptions());

                displayImage.Source = bitmapSource;

                // 창 제목에 해상도 정보 표시
                imageWindow.Title = $"Lost Ark 캡처 - {bitmap.Width}x{bitmap.Height}";
            }
            finally
            {
                DeleteObject(hBitmap);
                bitmap.Dispose();
            }
        }

        [DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);

        private void Grid_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (!isOcrModeEnabled) return;

            var grid = sender as Grid;
            if (grid == null) return;

            isSelecting = true;
            startPoint = e.GetPosition(displayImage);
            endPoint = startPoint;

            // 마우스 캡처
            grid.CaptureMouse();

            // 기존 선택 영역 제거
            if (selectionCanvas != null)
            {
                grid.Children.Remove(selectionCanvas);
            }

            // 새로운 선택 캔버스 생성
            selectionCanvas = new Canvas
            {
                Background = System.Windows.Media.Brushes.Transparent,
                IsHitTestVisible = false
            };

            grid.Children.Add(selectionCanvas);
        }

        private void Grid_MouseMove(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (!isOcrModeEnabled || !isSelecting || selectionCanvas == null) return;

            endPoint = e.GetPosition(displayImage);
            DrawSelectionRectangle();
        }

        private void Grid_MouseLeftButtonUp(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (!isOcrModeEnabled || !isSelecting) return;

            var grid = sender as Grid;
            if (grid == null) return;

            isSelecting = false;
            grid.ReleaseMouseCapture();

            endPoint = e.GetPosition(displayImage);

            // 선택된 영역 계산
            double left = Math.Min(startPoint.X, endPoint.X);
            double top = Math.Min(startPoint.Y, endPoint.Y);
            double width = Math.Abs(endPoint.X - startPoint.X);
            double height = Math.Abs(endPoint.Y - startPoint.Y);

            if (width > 5 && height > 5) // 최소 크기 체크
            {
                selectedOcrArea = new Rectangle((int)left, (int)top, (int)width, (int)height);
                StatusText.Text = $"OCR 영역 선택됨: {(int)width}x{(int)height}";
                DrawSelectionRectangle();
            }
            else
            {
                // 너무 작은 영역은 무시
                if (selectionCanvas != null && grid != null)
                {
                    grid.Children.Remove(selectionCanvas);
                    selectionCanvas = null;
                }
                StatusText.Text = "OCR 영역을 다시 선택하세요 (최소 5x5 픽셀)";
            }
        }

        private void DrawSelectionRectangle()
        {
            if (selectionCanvas == null) return;

            selectionCanvas.Children.Clear();

            double left = Math.Min(startPoint.X, endPoint.X);
            double top = Math.Min(startPoint.Y, endPoint.Y);
            double width = Math.Abs(endPoint.X - startPoint.X);
            double height = Math.Abs(endPoint.Y - startPoint.Y);

            if (width < 1 || height < 1) return;

            // 빨간색 5px 테두리 사각형 생성
            var rectangle = new System.Windows.Shapes.Rectangle
            {
                Width = width,
                Height = height,
                Stroke = System.Windows.Media.Brushes.Red,
                StrokeThickness = 5,
                Fill = System.Windows.Media.Brushes.Transparent
            };

            // 캔버스에 위치 설정
            Canvas.SetLeft(rectangle, left);
            Canvas.SetTop(rectangle, top);

            selectionCanvas.Children.Add(rectangle);

            // 선택 영역 크기 표시 텍스트
            var sizeText = new TextBlock
            {
                Text = $"{(int)width}x{(int)height}",
                Foreground = System.Windows.Media.Brushes.Red,
                Background = System.Windows.Media.Brushes.White,
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                Padding = new Thickness(2)
            };

            // 텍스트 위치 (사각형 위쪽)
            Canvas.SetLeft(sizeText, left);
            Canvas.SetTop(sizeText, Math.Max(0, top - 20));

            selectionCanvas.Children.Add(sizeText);
        }

        protected override void OnClosed(EventArgs e)
        {
            captureTimer?.Stop();
            outputDuplication?.Dispose();
            device?.Dispose();
            base.OnClosed(e);
        }
    }
}